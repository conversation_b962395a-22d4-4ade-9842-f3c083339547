#define CL_TARGET_OPENCL_VERSION 300
#include <CL/cl.h>
#include <omp.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <chrono>
#include <thread>
#include <atomic>

using namespace std;
using namespace std::chrono;

#define d 256

class FileLoader {
public:
    struct Config {
        size_t chunk_size = 8192;           // Read in 8KB chunks
        size_t max_file_size = 1024 * 1024 * 1024; // 1GB limit
        bool validate_file_size = true;
        bool use_memory_mapping = false;    // For very large files

        Config() 
            : chunk_size(8192)                          // 8KB mặc định
            , max_file_size(1024 * 1024 * 1024)        // 1GB mặc định  
            , validate_file_size(true)
            , use_memory_mapping(false)
        {}
        
        // Constructor với tham số (tùy chọn)
        Config(size_t chunk, size_t max_size, bool validate = true, bool memory_map = false)
            : chunk_size(chunk)
            , max_file_size(max_size)
            , validate_file_size(validate)
            , use_memory_mapping(memory_map)
        {}
    };

    static std::vector<char> loadFile(const std::string& filename, const Config& config = {}) {
        validateInput(filename, config);
        
        const auto file_size = getFileSize(filename);
        if (file_size == 0) return {};
        
        if (config.use_memory_mapping && file_size > config.chunk_size * 100) {
            return loadWithMemoryMapping(filename, file_size);
        }
        
        return loadWithBufferedRead(filename, file_size, config.chunk_size);
    }

private:
    static void validateInput(const std::string& filename, const Config& config) {
        if (filename.empty()) {
        }
        
        if (config.validate_file_size) {
            std::error_code ec;
            const auto size = std::filesystem::file_size(filename, ec);
            if (ec) {
            }
            if (size > config.max_file_size) {
            }
        }
    }
    
    static std::uintmax_t getFileSize(const std::string& filename) {
        std::error_code ec;
        return std::filesystem::file_size(filename, ec);
    }
    
    static std::vector<char> loadWithBufferedRead(const std::string& filename, 
                                                  std::uintmax_t file_size, 
                                                  size_t chunk_size) {
        std::ifstream file(filename, std::ios::binary);
        if (!file) {
        }
        
        std::vector<char> buffer;
        buffer.reserve(static_cast<size_t>(file_size));
        
        std::vector<char> chunk(chunk_size);
        while (file.read(chunk.data(), chunk_size) || file.gcount() > 0) {
            buffer.insert(buffer.end(), chunk.begin(), chunk.begin() + file.gcount());
        }
        
        return buffer;
    }
    
    static std::vector<char> loadWithMemoryMapping(const std::string& filename, std::uintmax_t file_size) {
        // Memory mapping implementation would go here
        // This is a placeholder - actual implementation would use platform-specific APIs
        return loadWithBufferedRead(filename, file_size, 65536); // Fallback to buffered read
    }
};

vector<char> fast_load_file(const string& filename) {
    ifstream file(filename, ios::binary | ios::ate);
    if (!file) { cerr << "Cannot open file\n"; exit(1); }
    streamsize size = file.tellg();
    file.seekg(0, ios::beg);
    vector<char> buffer(size);
    if (!file.read(buffer.data(), size)) { cerr << "Error reading file\n"; exit(1); }
    return buffer;
}

int rabin_karp_sequence(const string& pat, const vector<char>& txt, int q) {
    int M = pat.length(), N = txt.size(), p = 0, t = 0, h = 1, count = 0;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) {
        p = (d * p + pat[i]) % q;
        t = (d * t + txt[i]) % q;
    }
    for (int i = 0; i <= N - M; ++i) {
        if (p == t) {
            int j = 0;
            while (j < M && txt[i + j] == pat[j]) ++j;
            if (j == M) ++count;
        }
        if (i < N - M) {
            t = (d * (t - txt[i] * h) + txt[i + M]) % q;
            if (t < 0) t += q;
        }
    }
    return count;
}

int rabin_karp_omp(const string& pat, const vector<char>& txt, int q, int num_threads) {
    int M = pat.length(), N = txt.size();
    if (M > N) return 0;

    int h = 1;
    for (int i = 0; i < M - 1; ++i)
        h = (h * d) % q;

    int pattern_hash = 0;
    for (int i = 0; i < M; ++i)
        pattern_hash = (d * pattern_hash + pat[i]) % q;

    int count = 0;
    int chunk_size = (N - M + 1 + num_threads - 1) / num_threads;

#pragma omp parallel for reduction(+:count) num_threads(num_threads) schedule(static)
    for (int chunk = 0; chunk < num_threads; ++chunk) {
        int start = chunk * chunk_size;
        int end = min(start + chunk_size + (M - 1), N - M + 1);

        if (start >= end) continue;

        int t = 0;
        for (int j = 0; j < M; ++j)
            t = (d * t + txt[start + j]) % q;

        if (t == pattern_hash) {
            bool match = true;
            for (int j = 0; j < M; ++j) {
                if (txt[start + j] != pat[j]) {
                    match = false;
                    break;
                }
            }
            if (match) ++count;
        }

        for (int i = start + 1; i < end; ++i) {
            t = (d * (t - txt[i - 1] * h) + txt[i + M - 1]) % q;
            if (t < 0) t += q;

            if (t == pattern_hash) {
                int j = 0;
                while (j < M && txt[i + j] == pat[j]) ++j;
                if (j == M) ++count;

            }
        }
    }

    return count;
}

void worker(const string& pat, const vector<char>& txt, int q,
    int start, int end, atomic<int>& count) {
    int M = pat.length();
    int h = 1;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;

    int pattern_hash = 0;
    for (int i = 0; i < M; ++i) pattern_hash = (d * pattern_hash + pat[i]) % q;

    // Rolling hash nội bộ
    int t = 0;
    for (int j = 0; j < M; ++j)
        t = (d * t + txt[start + j]) % q;

    if (t == pattern_hash && memcmp(&txt[start], pat.data(), M) == 0)
        ++count;

    for (int i = start + 1; i < end; ++i) {
        t = (d * (t - txt[i - 1] * h) + txt[i + M - 1]) % q;
        if (t < 0) t += q;

        if (t == pattern_hash && memcmp(&txt[i], pat.data(), M) == 0)
            ++count;
    }
}

// Rabin-Karp song song hóa bằng std::thread
int rabin_karp_threaded(const string& pat, const vector<char>& txt, int q, int num_threads,
    double& compute_time) {
    int M = pat.length(), N = txt.size();
    if (M > N) return 0;

    int chunk_size = (N - M + 1 + num_threads - 1) / num_threads;

    std::atomic<int> count(0);
    vector<thread> threads;

    auto t_start = high_resolution_clock::now();

    for (int t = 0; t < num_threads; ++t) {
        int start = t * chunk_size;
        int end = min(start + chunk_size + (M - 1), N - M + 1); // Đệm M-1 ký tự cho biên

        if (start < end)
            threads.emplace_back(worker, cref(pat), cref(txt), q, start, end, ref(count));
    }

    for (auto& th : threads) th.join();

    auto t_end = high_resolution_clock::now();
    compute_time = duration_cast<duration<double, milli>>(t_end - t_start).count();

    return count;
}

const char* kernel_code = R"CLC(
#define d 256
__kernel void rabin_karp_chunked(
    __global const char* text,
    __global const char* pattern,
    const int M,
    const int text_size,
    const int q,
    const int pattern_hash,
    const int h,
    __global int* result,
    const int chunk_size) {

    int chunk_id = get_global_id(0);
    int start = chunk_id * chunk_size;
    int end = min(start + chunk_size + (M - 1), text_size - M + 1);

    if (start >= end) return;

    int t = 0;
    for (int j = 0; j < M; ++j)
        t = (d * t + text[start + j]) % q;

    if (t == pattern_hash) {
        int j;
        for (j = 0; j < M && text[start + j] == pattern[j]; ++j);
        if (j == M) result[start] = 1;
    }

    for (int i = start + 1; i < end; ++i) {
        t = (d * (t - text[i - 1] * h) + text[i + M - 1]) % q;
        if (t < 0) t += q;
        if (t == pattern_hash) {
            int j;
            for (j = 0; j < M && text[i + j] == pattern[j]; ++j);
            if (j == M) result[i] = 1;
        }
    }
}
)CLC";

int rabin_karp_opencl(const string& pat, const vector<char>& txt,
    int& matches, double& kernel_ms, double& io_ms, cl_device_id device, int q) {

    int M = pat.length(), N = txt.size(), h = 1, p_hash = 0;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) p_hash = (d * p_hash + pat[i]) % q;

    const int max_chunks = 8192;
    int chunk_size = max(256, (N - M + max_chunks - 1) / max_chunks);
    size_t num_chunks = (N - M + chunk_size - 1) / chunk_size;

    matches = 0;

    auto t1 = high_resolution_clock::now();

    cl_context ctx = clCreateContext(nullptr, 1, &device, nullptr, nullptr, nullptr);
    cl_queue_properties props[] = {
     CL_QUEUE_PROPERTIES, CL_QUEUE_PROFILING_ENABLE, 0
    };

    cl_command_queue queue = clCreateCommandQueueWithProperties(
        ctx, device, props, nullptr);

    cl_program prog = clCreateProgramWithSource(ctx, 1, &kernel_code, nullptr, nullptr);
    clBuildProgram(prog, 1, &device, nullptr, nullptr, nullptr);
    cl_kernel kernel = clCreateKernel(prog, "rabin_karp_chunked", nullptr);

    cl_mem txt_buf = clCreateBuffer(ctx, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, N, (void*)txt.data(), nullptr);
    cl_mem pat_buf = clCreateBuffer(ctx, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, M, (void*)pat.data(), nullptr);
    cl_mem res_buf = clCreateBuffer(ctx, CL_MEM_WRITE_ONLY, sizeof(int) * (N - M + 1), nullptr, nullptr);
    vector<int> res(N - M + 1, 0);

    clSetKernelArg(kernel, 0, sizeof(cl_mem), &txt_buf);
    clSetKernelArg(kernel, 1, sizeof(cl_mem), &pat_buf);
    clSetKernelArg(kernel, 2, sizeof(int), &M);
    clSetKernelArg(kernel, 3, sizeof(int), &N);
    clSetKernelArg(kernel, 4, sizeof(int), &q);
    clSetKernelArg(kernel, 5, sizeof(int), &p_hash);
    clSetKernelArg(kernel, 6, sizeof(int), &h);
    clSetKernelArg(kernel, 7, sizeof(cl_mem), &res_buf);
    clSetKernelArg(kernel, 8, sizeof(int), &chunk_size);

    auto t2 = high_resolution_clock::now();

    cl_event e;
    clEnqueueNDRangeKernel(queue, kernel, 1, nullptr, &num_chunks, nullptr, 0, nullptr, &e);
    clFinish(queue);

    cl_ulong st, en;
    clGetEventProfilingInfo(e, CL_PROFILING_COMMAND_START, sizeof(cl_ulong), &st, nullptr);
    clGetEventProfilingInfo(e, CL_PROFILING_COMMAND_END, sizeof(cl_ulong), &en, nullptr);
    kernel_ms = (en - st) / 1e6;

    auto t3 = high_resolution_clock::now();
    clEnqueueReadBuffer(queue, res_buf, CL_TRUE, 0, sizeof(int) * res.size(), res.data(), 0, nullptr, nullptr);
    for (int x : res) matches += x;
    auto t4 = high_resolution_clock::now();

    io_ms = duration_cast<milliseconds>(t2 - t1).count() + duration_cast<milliseconds>(t4 - t3).count();

    clReleaseEvent(e);
    clReleaseMemObject(txt_buf);
    clReleaseMemObject(pat_buf);
    clReleaseMemObject(res_buf);
    clReleaseKernel(kernel);
    clReleaseProgram(prog);
    clReleaseCommandQueue(queue);
    clReleaseContext(ctx);

    return 0;
}

int main() {
    ios::sync_with_stdio(false); cin.tie(nullptr);
    
    cout << "Use CPU (0) or GPU (1): ";
    
    //int choice; cin >> choice;
    // Nếu dùng CPU thì đặt use_cpu = true, nếu dùng GPU thì đặt use_cpu = false.
    bool use_cpu = false;
    auto srt1 = high_resolution_clock::now();
    vector<char> txt = fast_load_file("generated_1GB.txt");
    auto end1 = high_resolution_clock::now();

    
    cout << "Time to load file: " << duration_cast<duration<double, nanosecond>>(end1 - srt1).count() << " ms" << endl;
    auto srt2 = high_resolution_clock::now();
    auto pat = FileLoader::loadFile("generated_1GB.txt", config);
    auto end2 = high_resolution_clock::now();
    cout << "Time to load file: " << duration_cast<duration<double, nanosecond>>(end2 - srt2).count() << " ms" << endl;
    return 0;

    // Đọc tất cả pattern vào vector
    ifstream f("pat.txt");
    vector<string> patterns;
    string pat;
    while (getline(f, pat)) {
        if (!pat.empty()) patterns.push_back(pat);
    }
    f.close();

    int q = 101;
    // Dùng CPU thì mình sẽ tuỳ ý điều chỉnh số lượng thread (từ 2 -> 8 thread), còn nếu dùng GPU thì sẽ không cần thiết phải điều chỉnh.
    int threads = 8;
    // Máy anh em nếu có nhiều hơn 1 GPU thì vào taskmanager để xem id của GPU, nếu không thì cứ để mặc định là GPU số 0.
    int sel = 0; // Chọn thiết bị GPU nếu không dùng CPU
    cl_device_id dev = nullptr;

    // Hỏi một lần số thread hoặc device
    if (use_cpu) {
        //cout << "Enter number of threads: ";
        //cin >> threads;
    }
    else {
        cl_uint n;
        clGetPlatformIDs(0, nullptr, &n);
        vector<cl_platform_id> platforms(n);
        clGetPlatformIDs(n, platforms.data(), nullptr);

        vector<cl_device_id> devices;
        for (auto& p : platforms) {
            cl_uint num_dev;
            clGetDeviceIDs(p, CL_DEVICE_TYPE_GPU, 0, nullptr, &num_dev);
            vector<cl_device_id> devs(num_dev);
            clGetDeviceIDs(p, CL_DEVICE_TYPE_GPU, num_dev, devs.data(), nullptr);
            devices.insert(devices.end(), devs.begin(), devs.end());
        }

        for (int i = 0; i < devices.size(); ++i) {
            char name[256];
            clGetDeviceInfo(devices[i], CL_DEVICE_NAME, sizeof(name), name, nullptr);
            cout << "[" << i << "] " << name << "\n";
        }
        //cout << "Select device index: ";
        //cin >> sel;
        dev = devices[sel];
    }

    // Vòng lặp cho mỗi pattern
    for (const string& pat : patterns) {
        cout << "\nPattern: " << pat << endl;

        // Sequence baseline
        cout << "Waiting for calculation sequence..." << endl;
        auto t0 = high_resolution_clock::now();
        int seq_res = rabin_karp_sequence(pat, txt, q);
        auto t1 = high_resolution_clock::now();
        double seq_ms = duration_cast<milliseconds>(t1 - t0).count();
        cout << "Baseline Sequence Result: " << seq_res << ", Time: " << seq_ms << " ms\n";

        if (use_cpu) {
            double compute_time = 0;
            int res = rabin_karp_threaded(pat, txt, q, threads, compute_time);
            cout << "CPU Result: " << res << " matches, Time: " << compute_time << " ms\n";
            cout << "Speedup: " << seq_ms / compute_time << "x\n";
        }
        else {
            int result = 0;
            double kernel_ms = 0, io_ms = 0;
            rabin_karp_opencl(pat, txt, result, kernel_ms, io_ms, dev, q);

            cout << "GPU OpenCL Result: " << result << " matches\n";
            cout << "Kernel Time: " << kernel_ms << " ms, I/O Time: " << io_ms << " ms\n";
            cout << "Speedup (Seq / Kernel): " << seq_ms / kernel_ms << "x\n";
        }
    }
    return 0;
}
